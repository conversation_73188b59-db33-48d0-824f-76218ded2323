import React from 'react'
import { Plus, Trash2 } from 'lucide-react'

const ProctorTimingsSection = ({
  proctorTimings,
  setProctorTimings,
  proctorOptions,
  excludeProctorAuthors,
  setExcludeProctorAuthors
}) => {
  // Add new proctor timing
  const addProctorTiming = () => {
    const newId = proctorTimings.length > 0 ? Math.max(...proctorTimings.map(t => t.id)) + 1 : 1
    const newTiming = {
      id: newId,
      startTime: '9:00 AM',
      endTime: '11:00 AM',
      proctors: 'All selected'
    }
    setProctorTimings([...proctorTimings, newTiming])
  }

  // Delete proctor timing
  const deleteProctorTiming = (id) => {
    if (proctorTimings.length > 1) {
      setProctorTimings(proctorTimings.filter(t => t.id !== id))
    } else {
      alert('Cannot delete the last proctor timing slot')
    }
  }

  // Update proctor timing
  const updateProctorTiming = (id, field, value) => {
    setProctorTimings(proctorTimings.map(t =>
      t.id === id ? { ...t, [field]: value } : t
    ))
  }

  return (
    <div className="section">
      <div className="section-header">
        <h2>Proctor Duty Timings</h2>
        <a href="#" className="global-settings">Global Exam Settings</a>
      </div>
      <p className="section-subtitle">Set your institution's daily proctor duty timings here</p>

      {/* Table Header */}
      <div className="proctor-table-header">
        <div className="col-time">Start time</div>
        <div className="col-time">End time</div>
        <div className="col-dropdown">Proctors Involved</div>
        <div className="col-action"></div>
      </div>

      {/* Proctor Timing Rows */}
      {proctorTimings.map((timing) => (
        <div key={timing.id} className="proctor-table-row">
          <div className="slot-label">
            <span className="slot-number">Slot<br/>{String(timing.id).padStart(2, '0')}</span>
          </div>
          <div className="time-input">
            <input
              type="text"
              value={timing.startTime}
              onChange={(e) => updateProctorTiming(timing.id, 'startTime', e.target.value)}
            />
          </div>
          <div className="time-separator">to</div>
          <div className="time-input">
            <input
              type="text"
              value={timing.endTime}
              onChange={(e) => updateProctorTiming(timing.id, 'endTime', e.target.value)}
            />
          </div>
          <div className="dropdown-container">
            <select
              value={timing.proctors}
              onChange={(e) => updateProctorTiming(timing.id, 'proctors', e.target.value)}
            >
              <option value="All selected">(100) All selected</option>
              {proctorOptions.map(proctor => (
                <option key={proctor} value={proctor}>{proctor}</option>
              ))}
            </select>
          </div>
          <button
            className="delete-btn"
            onClick={() => deleteProctorTiming(timing.id)}
          >
            <Trash2 size={16} />
          </button>
        </div>
      ))}

      {/* Add Slot Button */}
      <button className="add-slot-btn" onClick={addProctorTiming}>
        <Plus size={16} />
        Slot {String(proctorTimings.length + 1).padStart(2, '0')}
      </button>

      {/* Checkbox */}
      <div className="checkbox-container">
        <input
          type="checkbox"
          id="excludeProctors"
          checked={excludeProctorAuthors}
          onChange={(e) => setExcludeProctorAuthors(e.target.checked)}
        />
        <label htmlFor="excludeProctors">
          Exclude proctors who are authors of items/assessments for this exam
        </label>
      </div>
    </div>
  )
}

export default ProctorTimingsSection
