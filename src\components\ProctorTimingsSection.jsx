import React from 'react'
import {
  Card,
  CardContent,
  Typography,
  Button,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  IconButton,
  Box,
  Chip,
  Link,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  FormControlLabel,
  Checkbox,
  OutlinedInput,
  ListItemText
} from '@mui/material'
import { Add, Delete, AccessTime } from '@mui/icons-material'
import { TimePicker } from '@mui/x-date-pickers/TimePicker'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import dayjs from 'dayjs'
import MaterialTimePicker from './MaterialTimePicker'

const ProctorTimingsSection = ({
  proctorTimings,
  setProctorTimings,
  proctorOptions,
  excludeProctorAuthors,
  setExcludeProctorAuthors
}) => {
  // Add new proctor timing
  const addProctorTiming = () => {
    const newId = proctorTimings.length > 0 ? Math.max(...proctorTimings.map(t => t.id)) + 1 : 1
    const newTiming = {
      id: newId,
      startTime: dayjs().hour(9).minute(0),
      endTime: dayjs().hour(11).minute(0),
      proctors: []
    }
    setProctorTimings([...proctorTimings, newTiming])
  }

  // Delete proctor timing
  const deleteProctorTiming = (id) => {
    if (proctorTimings.length > 1) {
      setProctorTimings(proctorTimings.filter(t => t.id !== id))
    } else {
      alert('Cannot delete the last proctor timing slot')
    }
  }

  // Update proctor timing
  const updateProctorTiming = (id, field, value) => {
    setProctorTimings(proctorTimings.map(t =>
      t.id === id ? { ...t, [field]: value } : t
    ))
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Card elevation={2}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
            <Typography variant="h5" component="h2" sx={{ fontWeight: 600 }}>
              Proctor Duty Timings
            </Typography>
            <Link href="#" color="primary" sx={{ textDecoration: 'none' }}>
              Global Exam Settings
            </Link>
          </Box>

          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Set your institution's daily proctor duty timings here
          </Typography>

        {/* Table */}
        <TableContainer component={Paper} variant="outlined">
          <Table>
            <TableHead>
              <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
                <TableCell sx={{ fontWeight: 600 }}>Slot</TableCell>
                <TableCell sx={{ fontWeight: 600 }}>Start Time</TableCell>
                <TableCell sx={{ fontWeight: 600 }}>End Time</TableCell>
                <TableCell sx={{ fontWeight: 600 }}>Proctors Involved</TableCell>
                <TableCell sx={{ fontWeight: 600, width: 60 }}>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {/* Proctor Timing Rows */}
              {proctorTimings.map((timing) => (
                <TableRow key={timing.id} hover>
                  <TableCell>
                    <Chip
                      label={`Slot ${String(timing.id).padStart(2, '0')}`}
                      size="small"
                      variant="outlined"
                    />
                  </TableCell>
                  <TableCell>
                    <MaterialTimePicker
                      value={timing.startTime}
                      onChange={(newValue) => updateProctorTiming(timing.id, 'startTime', newValue)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <TimePicker
                      value={timing.endTime}
                      onChange={(newValue) => updateProctorTiming(timing.id, 'endTime', newValue)}
                      views={['hours', 'minutes']}
                      format="h:mm a"
                      openTo="hours"
                      ampm={true}
                      slotProps={{
                        textField: {
                          size: 'small',
                          variant: 'outlined',
                          sx: {
                            width: 140,
                            '& .MuiInputBase-input': {
                              fontSize: '0.875rem'
                            }
                          }
                        },
                        popper: {
                          placement: 'bottom-start',
                          sx: {
                            '& .MuiPaper-root': {
                              boxShadow: '0px 5px 5px -3px rgba(0,0,0,0.2), 0px 8px 10px 1px rgba(0,0,0,0.14), 0px 3px 14px 2px rgba(0,0,0,0.12)',
                              borderRadius: '8px'
                            }
                          }
                        },
                        layout: {
                          sx: {
                            '& .MuiTimeClock-root': {
                              backgroundColor: 'background.paper'
                            },
                            '& .MuiClockPointer-root': {
                              backgroundColor: 'primary.main'
                            },
                            '& .MuiClockPointer-thumb': {
                              backgroundColor: 'primary.main',
                              border: '2px solid white'
                            }
                          }
                        }
                      }}
                      slots={{
                        openPickerIcon: AccessTime
                      }}
                    />
                  </TableCell>
                  <TableCell>
                    <FormControl size="small" sx={{ minWidth: 200 }}>
                      <InputLabel>Proctors</InputLabel>
                      <Select
                        multiple
                        value={timing.proctors}
                        onChange={(e) => updateProctorTiming(timing.id, 'proctors', e.target.value)}
                        input={<OutlinedInput label="Proctors" />}
                        renderValue={(selected) => (
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                            {selected.length === 0 ? (
                              <Typography variant="body2" color="text.secondary">
                                None selected
                              </Typography>
                            ) : selected.length === proctorOptions.length ? (
                              <Chip size="small" label={`All (${selected.length})`} />
                            ) : selected.length > 4 ? (
                              <Chip size="small" label={`${selected.length} proctors selected`} />
                            ) : (
                              selected.map((value) => (
                                <Chip key={value} label={value} size="small" />
                              ))
                            )}
                          </Box>
                        )}
                      >
                        {proctorOptions.map((proctor) => (
                          <MenuItem key={proctor} value={proctor}>
                            <Checkbox checked={timing.proctors.indexOf(proctor) > -1} />
                            <ListItemText primary={proctor} />
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </TableCell>
                  <TableCell>
                    <IconButton
                      color="error"
                      onClick={() => deleteProctorTiming(timing.id)}
                      size="small"
                    >
                      <Delete />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Add Slot Button */}
        <Box sx={{ mt: 2, mb: 2 }}>
          <Button
            variant="outlined"
            startIcon={<Add />}
            onClick={addProctorTiming}
            color="primary"
          >
            Slot {String(proctorTimings.length + 1).padStart(2, '0')}
          </Button>
        </Box>

        {/* Checkbox */}
        <Box sx={{ borderTop: 1, borderColor: 'divider', pt: 2 }}>
          <FormControlLabel
            control={
              <Checkbox
                checked={excludeProctorAuthors}
                onChange={(e) => setExcludeProctorAuthors(e.target.checked)}
                color="primary"
              />
            }
            label="Exclude proctors who are authors of items/assessments for this exam"
          />
        </Box>
      </CardContent>
    </Card>
    </LocalizationProvider>
  )
}

export default ProctorTimingsSection
