import React, { useState } from 'react'
import { ThemeProvider, createTheme } from '@mui/material/styles'
import { CssBaseline, Container, Box } from '@mui/material'
import ExamTimingsSection from './components/ExamTimingsSection'
import ProctorTimingsSection from './components/ProctorTimingsSection'
import DataSummary from './components/DataSummary'
import ExamScheduleSettings from './components/ExamScheduleSettings'
import CourseManagement from './components/CourseManagement'
import dayjs from 'dayjs'

// Material Design theme
const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
    background: {
      default: '#f5f5f5',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
  },
  components: {
    MuiCard: {
      styleOverrides: {
        root: {
          boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)',
          borderRadius: '8px',
        },
      },
    },
  },
})

function App() {
  // State for exam timings
  const [examTimings, setExamTimings] = useState([
    {
      id: 1,
      label: 'Morning',
      startTime: dayjs().hour(9).minute(0),
      endTime: dayjs().hour(11).minute(0),
      programs: ['Computer Science', 'Information Technology', 'Software Engineering', 'Data Science', 'Cybersecurity'],
      courses: ['Programming Fundamentals', 'Data Structures', 'Web Development', 'Database Management', 'Mobile App Development']
    },
    {
      id: 2,
      label: 'Afternoon',
      startTime: dayjs().hour(12).minute(0),
      endTime: dayjs().hour(14).minute(0),
      programs: ['Business Administration'],
      courses: ['Project Management', 'Digital Marketing']
    },
    {
      id: 3,
      label: 'Evening',
      startTime: dayjs().hour(15).minute(0),
      endTime: dayjs().hour(17).minute(0),
      programs: ['Data Science', 'Cybersecurity'],
      courses: ['Machine Learning', 'Network Security']
    }
  ])

  // State for proctor duty timings
  const [proctorTimings, setProctorTimings] = useState([
    {
      id: 1,
      startTime: dayjs().hour(9).minute(0),
      endTime: dayjs().hour(11).minute(0),
      proctors: ['Dr. Smith', 'Prof. Johnson', 'Dr. Williams', 'Prof. Brown', 'Dr. Davis']
    },
    {
      id: 2,
      startTime: dayjs().hour(12).minute(0),
      endTime: dayjs().hour(14).minute(0),
      proctors: ['Dr. Williams', 'Prof. Brown']
    },
    {
      id: 3,
      startTime: dayjs().hour(15).minute(0),
      endTime: dayjs().hour(17).minute(0),
      proctors: ['Dr. Davis', 'Prof. Miller']
    }
  ])

  const [excludeProctorAuthors, setExcludeProctorAuthors] = useState(true)

  // Program and course options
  const programOptions = [
    'Computer Science',
    'Information Technology',
    'Software Engineering',
    'Data Science',
    'Cybersecurity',
    'Business Administration',
    'Marketing',
    'Finance'
  ]
  const courseOptions = [
    'Programming Fundamentals',
    'Data Structures',
    'Database Management',
    'Web Development',
    'Mobile App Development',
    'Machine Learning',
    'Network Security',
    'Project Management',
    'Digital Marketing',
    'Financial Analysis',
    'Business Strategy',
    'Statistics'
  ]
  const proctorOptions = [
    'Dr. Smith',
    'Prof. Johnson',
    'Dr. Williams',
    'Prof. Brown',
    'Dr. Davis',
    'Prof. Miller',
    'Dr. Wilson',
    'Prof. Moore',
    'Dr. Taylor',
    'Prof. Anderson'
  ]



  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Box sx={{ backgroundColor: 'background.default', minHeight: '100vh', py: 3 }}>
        <Container maxWidth="xl">
          <ExamTimingsSection
            examTimings={examTimings}
            setExamTimings={setExamTimings}
            programOptions={programOptions}
            courseOptions={courseOptions}
          />

          <Box sx={{ mt: 3 }}>
            <ProctorTimingsSection
              proctorTimings={proctorTimings}
              setProctorTimings={setProctorTimings}
              proctorOptions={proctorOptions}
              excludeProctorAuthors={excludeProctorAuthors}
              setExcludeProctorAuthors={setExcludeProctorAuthors}
            />
          </Box>

          <Box sx={{ mt: 3 }}>
            <ExamScheduleSettings />
          </Box>

          <Box sx={{ mt: 3 }}>
            <CourseManagement />
          </Box>

          <Box sx={{ mt: 3 }}>
            <DataSummary
              examTimings={examTimings}
              proctorTimings={proctorTimings}
              excludeProctorAuthors={excludeProctorAuthors}
            />
          </Box>
        </Container>
      </Box>
    </ThemeProvider>
  )
}

export default App
