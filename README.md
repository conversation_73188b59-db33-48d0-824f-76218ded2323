# Exam Scheduling App

A React application for managing exam timings and proctor duty schedules with full CRUD (Create, Read, Update, Delete) operations.

## Features

### Exam Timings Per Day
- **Create**: Add new exam time slots with custom labels, start/end times, and applicable programs/courses
- **Read**: View all existing exam time slots in a structured table format
- **Update**: Edit slot labels, times, and dropdown selections in real-time
- **Delete**: Remove exam time slots with a single click

### Proctor Duty Timings
- **Create**: Add new proctor duty time slots with start/end times and proctor assignments
- **Read**: View all proctor duty schedules
- **Update**: Modify proctor duty times and assignments
- **Delete**: Remove proctor duty slots

### Additional Features
- Warning messages for timing conflicts
- Checkbox to exclude proctor authors from assignments
- Responsive design for mobile and desktop
- Real-time form validation
- Global exam settings links

## CRUD Operations

### Create Operations
1. **Add Exam Timing**: Click the "Slot XX" button to add a new exam time slot
2. **Add Proctor Timing**: Click the "Slot XX" button in the proctor section to add a new duty slot

### Read Operations
- All data is displayed in real-time in structured tables
- Slot numbers are automatically generated and displayed
- Current selections are shown in dropdowns

### Update Operations
1. **Edit Labels**: Click on any label input field to modify exam slot names
2. **Change Times**: Click on time input fields to update start/end times
3. **Update Selections**: Use dropdowns to change program, course, or proctor assignments
4. **Toggle Settings**: Use the checkbox to enable/disable proctor author exclusion

### Delete Operations
- Click the red trash icon (🗑️) next to any row to delete that time slot
- Deletion is immediate and updates the UI in real-time

## Getting Started

### Prerequisites
- Node.js (v14 or higher)
- npm or yarn

### Installation
1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

### Running the Application
```bash
npm run dev
```

The application will be available at `http://localhost:5173/`

### Building for Production
```bash
npm run build
```

## Technology Stack
- **React 18**: Frontend framework
- **Vite**: Build tool and development server
- **Lucide React**: Icon library
- **CSS3**: Styling with responsive design

## Component Structure
```
src/
├── App.jsx                 # Main application component
├── components/
│   ├── ExamTimingsSection.jsx    # Exam timings CRUD component
│   └── ProctorTimingsSection.jsx # Proctor timings CRUD component
├── App.css                 # Application styles
├── index.css              # Global styles
└── main.jsx               # Application entry point
```

## State Management
The application uses React's built-in `useState` hooks for state management:
- `examTimings`: Array of exam time slot objects
- `proctorTimings`: Array of proctor duty objects
- `excludeProctorAuthors`: Boolean for proctor exclusion setting

## Data Structure

### Exam Timing Object
```javascript
{
  id: number,
  label: string,
  startTime: string,
  endTime: string,
  programs: string,
  courses: string
}
```

### Proctor Timing Object
```javascript
{
  id: number,
  startTime: string,
  endTime: string,
  proctors: string
}
```

## Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test the CRUD operations
5. Submit a pull request

## License
MIT License
