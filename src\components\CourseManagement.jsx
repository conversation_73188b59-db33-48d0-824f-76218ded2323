import React, { useState } from 'react'
import {
  Box,
  Typography,
  TextField,
  Button,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  InputAdornment,
  IconButton,
  Pagination,
  FormControl,
  Select,
  MenuItem,
  Chip,
  Card,
  CardContent
} from '@mui/material'
import { Search, Sync, FilterList, Warning } from '@mui/icons-material'

const CourseManagement = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [page, setPage] = useState(1)
  const [rowsPerPage, setRowsPerPage] = useState(10)

  const courses = [
    {
      id: 'BON101',
      name: 'Basics of Neurology',
      details: 'MP • RT • CUR1 0 • Y1 • L1',
      duration: 120,
      syncedFrom: 'Not synced',
      studentsCount: '-',
      bufferMale: 0,
      bufferFemale: 0,
      hasError: false
    },
    {
      id: 'FUND103',
      name: 'Fundamentals of Human Body',
      details: 'MP • RT • CUR1 0 • Y1 • L1',
      duration: 60,
      syncedFrom: 'Not synced',
      studentsCount: '-',
      bufferMale: 0,
      bufferFemale: 0,
      hasError: false
    },
    {
      id: 'NEU103',
      name: 'Neurology of Science',
      details: 'MP • RT • CUR1 0 • Y1 • L1',
      duration: 90,
      syncedFrom: 'Not synced',
      studentsCount: '-',
      bufferMale: 0,
      bufferFemale: 0,
      hasError: false
    },
    {
      id: 'PHYS201',
      name: 'Principles of Physiology',
      details: 'MP • RT • CUR1 0 • Y1 • L1',
      duration: 0,
      syncedFrom: 'Not synced',
      studentsCount: '-',
      bufferMale: 0,
      bufferFemale: 0,
      hasError: true
    },
    {
      id: 'PHARM301',
      name: 'Pharmacology Fundamentals',
      details: 'MP • RT • CUR1 0 • Y1 • L1',
      duration: 60,
      syncedFrom: 'Not synced',
      studentsCount: '-',
      bufferMale: 0,
      bufferFemale: 0,
      hasError: false
    },
    {
      id: 'PATH202',
      name: 'Pathophysiology Insights',
      details: 'MP • RT • CUR1 0 • Y1 • L1',
      duration: 75,
      syncedFrom: 'Not synced',
      studentsCount: '-',
      bufferMale: 0,
      bufferFemale: 0,
      hasError: false
    },
    {
      id: 'MICR205',
      name: 'Microbiology Essentials',
      details: 'MP • RT • CUR1 0 • Y1 • L1',
      duration: 60,
      syncedFrom: 'Not synced',
      studentsCount: '-',
      bufferMale: 0,
      bufferFemale: 0,
      hasError: false
    },
    {
      id: 'GENE302',
      name: 'Genetics and Genomics',
      details: 'MP • RT • CUR1 0 • Y1 • L1',
      duration: 90,
      syncedFrom: 'Not synced',
      studentsCount: '-',
      bufferMale: 0,
      bufferFemale: 0,
      hasError: false
    },
    {
      id: 'NUTR204',
      name: 'Nutrition Science Basics',
      details: 'MP • RT • CUR1 0 • Y1 • L1',
      duration: 60,
      syncedFrom: 'Not synced',
      studentsCount: '-',
      bufferMale: 0,
      bufferFemale: 0,
      hasError: false
    },
    {
      id: 'ANAT106',
      name: 'Anatomy and Physiology I',
      details: 'MP • RT • CUR1 0 • Y1 • L1',
      duration: 90,
      syncedFrom: 'Not synced',
      studentsCount: '-',
      bufferMale: 0,
      bufferFemale: 0,
      hasError: false
    }
  ]

  const errorCourses = courses.filter(course => course.hasError || course.duration === 0)

  const handleDurationChange = (courseId, newDuration) => {
    // Handle duration change logic here
    console.log(`Updating duration for ${courseId} to ${newDuration}`)
  }

  const handleBufferChange = (courseId, field, value) => {
    // Handle buffer count change logic here
    console.log(`Updating ${field} for ${courseId} to ${value}`)
  }

  return (
    <Card elevation={2}>
      <CardContent>
        <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Box>
          <Typography variant="h6" component="h2" sx={{ fontWeight: 600, mb: 0.5 }}>
            Exam Duration / Students Involved Per Course
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Sync to upload students participating in this exam
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <TextField
            size="small"
            placeholder="Search course"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search fontSize="small" />
                </InputAdornment>
              ),
            }}
            sx={{ width: 250 }}
          />
          <Button
            variant="contained"
            startIcon={<Sync />}
            sx={{ textTransform: 'none' }}
          >
            Start Sync
          </Button>
          <IconButton>
            <FilterList />
          </IconButton>
        </Box>
      </Box>

      {/* Error Alert */}
      {errorCourses.length > 0 && (
        <Alert
          severity="error"
          icon={<Warning />}
          sx={{ mb: 3 }}
          action={
            <Button color="inherit" size="small">
              View ({errorCourses.length})
            </Button>
          }
        >
          ({errorCourses.length}) courses have no exam duration set. Please update now otherwise these courses will not be scheduled.
        </Alert>
      )}

      {/* Table */}
      <TableContainer component={Paper} variant="outlined" sx={{ boxShadow: 'none', border: '1px solid', borderColor: 'divider' }}>
        <Table>
          <TableHead>
            <TableRow sx={{ backgroundColor: '#f8f9fa' }}>
              <TableCell sx={{ fontWeight: 600, color: 'text.secondary', fontSize: '0.875rem' }}>
                Course Details
              </TableCell>
              <TableCell sx={{ fontWeight: 600, color: 'text.secondary', fontSize: '0.875rem' }}>
                Exam Duration<br />
                <Typography variant="caption" color="text.secondary">(Minutes)</Typography>
              </TableCell>
              <TableCell sx={{ fontWeight: 600, color: 'text.secondary', fontSize: '0.875rem' }}>
                Synced from
              </TableCell>
              <TableCell sx={{ fontWeight: 600, color: 'text.secondary', fontSize: '0.875rem' }}>
                Students Count
              </TableCell>
              <TableCell sx={{ fontWeight: 600, color: 'text.secondary', fontSize: '0.875rem' }}>
                Buffer Count<br />
                <Typography variant="caption" color="text.secondary">(Male)</Typography>
              </TableCell>
              <TableCell sx={{ fontWeight: 600, color: 'text.secondary', fontSize: '0.875rem' }}>
                Buffer Count<br />
                <Typography variant="caption" color="text.secondary">(Female)</Typography>
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {courses.slice((page - 1) * rowsPerPage, page * rowsPerPage).map((course) => (
              <TableRow key={course.id} hover>
                <TableCell>
                  <Box>
                    <Typography variant="body2" sx={{ fontWeight: 500, mb: 0.5 }}>
                      {course.id} - {course.name}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {course.details}
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell>
                  <TextField
                    size="small"
                    type="number"
                    value={course.duration}
                    onChange={(e) => handleDurationChange(course.id, e.target.value)}
                    sx={{
                      width: 80,
                      '& .MuiOutlinedInput-root': {
                        borderColor: course.hasError ? 'error.main' : 'divider'
                      }
                    }}
                    error={course.hasError}
                    InputProps={{
                      endAdornment: course.hasError && (
                        <InputAdornment position="end">
                          <Warning color="error" fontSize="small" />
                        </InputAdornment>
                      )
                    }}
                  />
                </TableCell>
                <TableCell>
                  <Typography variant="body2" color="text.secondary">
                    {course.syncedFrom}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {course.studentsCount}
                  </Typography>
                </TableCell>
                <TableCell>
                  <TextField
                    size="small"
                    type="number"
                    value={course.bufferMale}
                    onChange={(e) => handleBufferChange(course.id, 'bufferMale', e.target.value)}
                    sx={{ width: 60 }}
                  />
                </TableCell>
                <TableCell>
                  <TextField
                    size="small"
                    type="number"
                    value={course.bufferFemale}
                    onChange={(e) => handleBufferChange(course.id, 'bufferFemale', e.target.value)}
                    sx={{ width: 60 }}
                  />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Pagination */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 2 }}>
        <Pagination
          count={Math.ceil(courses.length / rowsPerPage)}
          page={page}
          onChange={(e, newPage) => setPage(newPage)}
          color="primary"
        />
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Typography variant="body2">Lists per page:</Typography>
          <FormControl size="small">
            <Select
              value={rowsPerPage}
              onChange={(e) => setRowsPerPage(e.target.value)}
              sx={{ minWidth: 60 }}
            >
              <MenuItem value={10}>10</MenuItem>
              <MenuItem value={25}>25</MenuItem>
              <MenuItem value={50}>50</MenuItem>
            </Select>
          </FormControl>
        </Box>
      </Box>
        </Box>
      </CardContent>
    </Card>
  )
}

export default CourseManagement
