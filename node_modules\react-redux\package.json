{"name": "react-redux", "version": "7.2.9", "description": "Official React bindings for Redux", "keywords": ["react", "reactjs", "redux"], "license": "MIT", "author": "<PERSON> <<EMAIL>> (https://github.com/gaearon)", "homepage": "https://github.com/reduxjs/react-redux", "repository": "github:reduxjs/react-redux", "bugs": "https://github.com/reduxjs/react-redux/issues", "main": "./lib/index.js", "unpkg": "dist/react-redux.js", "module": "es/index.js", "files": ["dist", "lib", "src", "es"], "scripts": {"build:commonjs": "cross-env BABEL_ENV=commonjs babel src --out-dir lib", "build:es": "babel src --out-dir es", "build:umd": "cross-env NODE_ENV=development rollup -c -o dist/react-redux.js", "build:umd:min": "cross-env NODE_ENV=production rollup -c -o dist/react-redux.min.js", "build": "npm run build:commonjs && npm run build:es && npm run build:umd && npm run build:umd:min", "clean": "rimraf lib dist es coverage", "format": "prettier --write \"{src,test}/**/*.{js,ts}\"  \"docs/**/*.md\"", "lint": "eslint src test/utils test/components test/hooks", "prepare": "npm run clean && npm run build", "pretest": "npm run lint", "test": "jest", "coverage": "codecov"}, "peerDependencies": {"react": "^16.8.3 || ^17 || ^18"}, "peerDependenciesMeta": {"react-dom": {"optional": true}, "react-native": {"optional": true}}, "dependencies": {"@babel/runtime": "^7.15.4", "@types/react-redux": "^7.1.20", "hoist-non-react-statics": "^3.3.2", "loose-envify": "^1.4.0", "prop-types": "^15.7.2", "react-is": "^17.0.2"}, "devDependencies": {"@babel/cli": "^7.15.7", "@babel/core": "^7.15.8", "@babel/plugin-proposal-decorators": "^7.15.8", "@babel/plugin-proposal-object-rest-spread": "^7.15.6", "@babel/plugin-transform-react-display-name": "^7.15.1", "@babel/plugin-transform-react-jsx": "^7.14.9", "@babel/plugin-transform-runtime": "^7.15.8", "@babel/preset-env": "^7.15.8", "@rollup/plugin-babel": "^5.3.0", "@rollup/plugin-commonjs": "^21.0.1", "@rollup/plugin-node-resolve": "^13.0.6", "@rollup/plugin-replace": "^3.0.0", "@testing-library/jest-dom": "^5.14.1", "@testing-library/jest-native": "^4.0.2", "@testing-library/react": "^12.1.2", "@testing-library/react-hooks": "^7.0.2", "@testing-library/react-native": "^8.0.0", "babel-eslint": "^10.1.0", "babel-jest": "^27.3.1", "codecov": "^3.8.3", "create-react-class": "^15.7.0", "cross-env": "^7.0.3", "es3ify": "^0.2.2", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.25.2", "eslint-plugin-react": "^7.26.1", "glob": "^7.2.0", "jest": "^27.3.1", "prettier": "^2.4.1", "react": "^17.0.2", "react-dom": "^17.0.2", "react-native": "^0.66.1", "react-test-renderer": "^17.0.2", "redux": "^4.1.1", "rimraf": "^3.0.2", "rollup": "^2.58.3", "rollup-plugin-terser": "^7.0.2"}, "browserify": {"transform": ["loose-envify"]}}