function r(r){return"Minified Redux error #"+r+"; visit https://redux.js.org/Errors?code="+r+" for the full message or use the non-minified dev environment for full errors. "}var t="function"==typeof Symbol&&Symbol.observable||"@@observable",n=function(){return Math.random().toString(36).substring(7).split("").join(".")},e={INIT:"@@redux/INIT"+n(),REPLACE:"@@redux/REPLACE"+n(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+n()}};function o(r){if("object"!=typeof r||null===r)return!1;for(var t=r;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(r)===t}function i(n,f,u){var c;if("function"==typeof f&&"function"==typeof u||"function"==typeof u&&"function"==typeof arguments[3])throw Error(r(0));if("function"==typeof f&&void 0===u&&(u=f,f=void 0),void 0!==u){if("function"!=typeof u)throw Error(r(1));return u(i)(n,f)}if("function"!=typeof n)throw Error(r(2));var p=n,a=f,y=[],l=y,s=!1;function v(){l===y&&(l=y.slice())}function h(){if(s)throw Error(r(3));return a}function b(t){if("function"!=typeof t)throw Error(r(4));if(s)throw Error(r(5));var n=!0;return v(),l.push(t),function(){if(n){if(s)throw Error(r(6));n=!1,v();var e=l.indexOf(t);l.splice(e,1),y=null}}}function O(t){if(!o(t))throw Error(r(7));if(void 0===t.type)throw Error(r(8));if(s)throw Error(r(9));try{s=!0,a=p(a,t)}finally{s=!1}for(var n=y=l,e=0;n.length>e;e++){(0,n[e])()}return t}function d(t){if("function"!=typeof t)throw Error(r(10));p=t,O({type:e.REPLACE})}function E(){var n,e=b;return(n={subscribe:function(t){if("object"!=typeof t||null===t)throw Error(r(11));function n(){t.next&&t.next(h())}return n(),{unsubscribe:e(n)}}})[t]=function(){return this},n}return O({type:e.INIT}),(c={dispatch:O,subscribe:b,getState:h,replaceReducer:d})[t]=E,c}var f=i;function u(t){for(var n=Object.keys(t),o={},i=0;n.length>i;i++){var f=n[i];"function"==typeof t[f]&&(o[f]=t[f])}var u,c=Object.keys(o);try{!function(t){Object.keys(t).forEach((function(n){var o=t[n];if(void 0===o(void 0,{type:e.INIT}))throw Error(r(12));if(void 0===o(void 0,{type:e.PROBE_UNKNOWN_ACTION()}))throw Error(r(13))}))}(o)}catch(r){u=r}return function(t,n){if(void 0===t&&(t={}),u)throw u;for(var e=!1,i={},f=0;c.length>f;f++){var p=c[f],a=t[p],y=(0,o[p])(a,n);if(void 0===y)throw Error(r(14));i[p]=y,e=e||y!==a}return(e=e||c.length!==Object.keys(t).length)?i:t}}function c(r,t){return function(){return t(r.apply(this,arguments))}}function p(t,n){if("function"==typeof t)return c(t,n);if("object"!=typeof t||null===t)throw Error(r(16));var e={};for(var o in t){var i=t[o];"function"==typeof i&&(e[o]=c(i,n))}return e}function a(r,t,n){return t in r?Object.defineProperty(r,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):r[t]=n,r}function y(r,t){var n=Object.keys(r);if(Object.getOwnPropertySymbols){var e=Object.getOwnPropertySymbols(r);t&&(e=e.filter((function(t){return Object.getOwnPropertyDescriptor(r,t).enumerable}))),n.push.apply(n,e)}return n}function l(r){for(var t=1;arguments.length>t;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?y(Object(n),!0).forEach((function(t){a(r,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(n)):y(Object(n)).forEach((function(t){Object.defineProperty(r,t,Object.getOwnPropertyDescriptor(n,t))}))}return r}function s(){for(var r=arguments.length,t=Array(r),n=0;r>n;n++)t[n]=arguments[n];return 0===t.length?function(r){return r}:1===t.length?t[0]:t.reduce((function(r,t){return function(){return r(t.apply(void 0,arguments))}}))}function v(){for(var t=arguments.length,n=Array(t),e=0;t>e;e++)n[e]=arguments[e];return function(t){return function(){var e=t.apply(void 0,arguments),o=function(){throw Error(r(15))},i={getState:e.getState,dispatch:function(){return o.apply(void 0,arguments)}},f=n.map((function(r){return r(i)}));return o=s.apply(void 0,f)(e.dispatch),l(l({},e),{},{dispatch:o})}}}export{e as __DO_NOT_USE__ActionTypes,v as applyMiddleware,p as bindActionCreators,u as combineReducers,s as compose,i as createStore,f as legacy_createStore};
