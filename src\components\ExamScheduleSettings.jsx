import React, { useState } from "react";
import {
  Box,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Link,
  Alert,
  RadioGroup,
  FormControlLabel,
  Radio,
  TextField,
  OutlinedInput,
  Checkbox,
  ListItemText,
  Chip,
} from "@mui/material";
import { Warning } from "@mui/icons-material";

const ExamScheduleSettings = () => {
  const [weeklySlots, setWeeklySlots] = useState({
    Sunday: [],
    Monday: ["Slot 1", "Slot 2", "Slot 3"],
    Tuesday: ["Slot 1"],
    Wednesday: ["Slot 1", "Slot 2"],
    Thursday: ["Slot 1", "Slot 2", "Slot 3"],
    Friday: ["Slot 3"],
    Saturday: [],
  });

  const [gapMethod, setGapMethod] = useState("common-all");
  const [commonGap, setCommonGap] = useState(2);

  const handleSlotChange = (day, value) => {
    setWeeklySlots((prev) => ({
      ...prev,
      [day]: value,
    }));
  };

  const handleClear = (day) => {
    setWeeklySlots((prev) => ({
      ...prev,
      [day]: [],
    }));
  };

  const slotOptions = ["Slot 1", "Slot 2", "Slot 3"];

  const renderSlotValue = (selected) => {
    if (selected.length === 0) {
      return (
        <Typography variant="body2" color="text.secondary">
          - Select -
        </Typography>
      );
    }
    if (selected.length === slotOptions.length) {
      return <Typography variant="body2">All slots</Typography>;
    }
    if (selected.length > 2) {
      return (
        <Typography variant="body2">{selected.length} selected</Typography>
      );
    }
    return (
      <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.5 }}>
        {selected.map((value) => (
          <Chip key={value} label={value} size="small" />
        ))}
      </Box>
    );
  };

  const days = [
    "Sunday",
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
  ];

  return (
    <Box sx={{ display: "flex", gap: 4, alignItems: "flex-start" }}>
      {/* Left Column - Exam Slots for Each Day */}
      <Box sx={{ flex: 1, minWidth: 0 }}>
        <Typography variant="h6" component="h2" sx={{ fontWeight: 600, mb: 1 }}>
          Exam Slots for Each Day in a Week
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Assign slots for each day in a week
        </Typography>

        <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
          {days.map((day) => (
            <Box
              key={day}
              sx={{ display: "flex", alignItems: "center", gap: 2 }}
            >
              <Typography
                variant="body2"
                sx={{
                  minWidth: 80,
                  fontWeight: 500,
                  color: "text.primary",
                }}
              >
                {day}
              </Typography>
              <Typography variant="body2" sx={{ mx: 1 }}>
                :
              </Typography>
              <FormControl size="small" sx={{ minWidth: 200 }}>
                <Select
                  multiple
                  value={weeklySlots[day]}
                  onChange={(e) => handleSlotChange(day, e.target.value)}
                  input={<OutlinedInput />}
                  displayEmpty
                  sx={{ fontSize: "0.875rem" }}
                  renderValue={renderSlotValue}
                >
                  {slotOptions.map((option) => (
                    <MenuItem key={option} value={option}>
                      <Checkbox
                        checked={weeklySlots[day].indexOf(option) > -1}
                      />
                      <ListItemText primary={option} />
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              <Button
                size="small"
                color="primary"
                onClick={() => handleClear(day)}
                sx={{ textTransform: "none", fontSize: "0.875rem" }}
              >
                Clear
              </Button>
            </Box>
          ))}
        </Box>
      </Box>
      {/* Right Column */}
      <Box sx={{ flex: 1, minWidth: 0 }}>
        {/* Exam Gap Section */}
        <Box sx={{ mb: 4 }}>
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              mb: 1,
            }}
          >
            <Typography variant="h6" component="h2" sx={{ fontWeight: 600 }}>
              Exam Gap
            </Typography>
            <Link
              href="#"
              color="primary"
              sx={{ textDecoration: "none", fontSize: "0.875rem" }}
            >
              Global Exam Settings
            </Link>
          </Box>

          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Set how exam gap should be managed across courses
          </Typography>

          <Typography variant="body2" sx={{ mb: 2, fontWeight: 500 }}>
            Select the method for applying gaps between exams:
          </Typography>

          <RadioGroup
            value={gapMethod}
            onChange={(e) => setGapMethod(e.target.value)}
            sx={{ mb: 3 }}
          >
            <FormControlLabel
              value="common-all"
              control={<Radio size="small" />}
              label={
                <Typography variant="body2">
                  Common Gap Across All Courses
                </Typography>
              }
            />
            <FormControlLabel
              value="common-program"
              control={<Radio size="small" />}
              label={
                <Typography variant="body2">
                  Common Gap Per Program (for all its courses)
                </Typography>
              }
            />
          </RadioGroup>

          <Box sx={{ display: "flex", alignItems: "center", gap: 2, mb: 2 }}>
            <Typography variant="body2" sx={{ fontWeight: 500 }}>
              Set a common gap:
            </Typography>
            <TextField
              size="small"
              type="number"
              value={commonGap}
              onChange={(e) => setCommonGap(e.target.value)}
              sx={{ width: 80 }}
              slotProps={{
                htmlInput: { min: 0 },
              }}
            />
            <Typography variant="body2" color="text.secondary">
              In Days
            </Typography>
          </Box>

          <Alert severity="warning" icon={<Warning />} sx={{ mb: 3 }}>
            Exam extends to 10/11/2025 due to 2 day gap, exceeding the set date
            range. Recommended gap: 1 day
          </Alert>
        </Box>

        {/* Exam Order Sequence */}
        <Box>
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              mb: 1,
            }}
          >
            <Typography variant="h6" component="h2" sx={{ fontWeight: 600 }}>
              Exam Order Sequence
            </Typography>
            <Link
              href="#"
              color="primary"
              sx={{ textDecoration: "none", fontSize: "0.875rem" }}
            >
              Global Exam Settings
            </Link>
          </Box>

          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Set exam order should be managed across courses
          </Typography>

          <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                p: 2,
                backgroundColor: "#f8f9fa",
                borderRadius: 1,
              }}
            >
              <Box>
                <Typography variant="body2" sx={{ fontWeight: 500 }}>
                  PE – Pediatrics
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                60 courses
              </Typography>
            </Box>

            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                p: 2,
                backgroundColor: "#f8f9fa",
                borderRadius: 1,
              }}
            >
              <Box>
                <Typography variant="body2" sx={{ fontWeight: 500 }}>
                  OB – Obstetrics & Gynecology
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                60 courses
              </Typography>
            </Box>
          </Box>

          <Box sx={{ display: "flex", justifyContent: "center", mt: 3 }}>
            <Button color="primary" sx={{ textTransform: "none" }}>
              Change Order
            </Button>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default ExamScheduleSettings;
