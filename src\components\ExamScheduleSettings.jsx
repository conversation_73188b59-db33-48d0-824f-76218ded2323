import React, { useState } from "react";
import {
  Box,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Link,
  Alert,
  RadioGroup,
  FormControlLabel,
  Radio,
  TextField,
  OutlinedInput,
  Checkbox,
  ListItemText,
  Chip,
  Card,
  CardContent,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  List,
  ListItem,
  Collapse,
} from "@mui/material";
import { Warning, Close, DragIndicator, ExpandMore, ExpandLess } from "@mui/icons-material";

const ExamScheduleSettings = () => {
  const [weeklySlots, setWeeklySlots] = useState({
    Sunday: [],
    Monday: ["Slot 1", "Slot 2", "Slot 3"],
    Tuesday: ["Slot 1"],
    Wednesday: ["Slot 1", "Slot 2"],
    Thursday: ["Slot 1", "Slot 2", "Slot 3"],
    Friday: ["Slot 3"],
    Saturday: [],
  });

  const [gapMethod, setGapMethod] = useState("common-all");
  const [commonGap, setCommonGap] = useState(2);
  const [orderDialogOpen, setOrderDialogOpen] = useState(false);
  const [expandedItems, setExpandedItems] = useState({
    "PE": true,
    "PE-Year1": true,
    "PE-Year1-Level1": true,
    "PE-Year1-Level2": true
  });

  const [examOrder, setExamOrder] = useState([
    {
      id: "PE",
      name: "PE – Pediatrics",
      type: "program",
      children: [
        {
          id: "PE-Year1",
          name: "Year 1",
          type: "year",
          children: [
            {
              id: "PE-Year1-Level1",
              name: "Level 1",
              type: "level",
              children: [
                { id: "PE-Y1-L1-C1", name: "Course Name 1", type: "course" },
                { id: "PE-Y1-L1-C2", name: "Course Name 2", type: "course" },
                { id: "PE-Y1-L1-C3", name: "Course Name 3", type: "course" }
              ]
            },
            {
              id: "PE-Year1-Level2",
              name: "Level 2",
              type: "level",
              children: [
                { id: "PE-Y1-L2-C1", name: "Course Name 1", type: "course" },
                { id: "PE-Y1-L2-C2", name: "Course Name 2", type: "course" },
                { id: "PE-Y1-L2-C3", name: "Course Name 3", type: "course" }
              ]
            }
          ]
        },
        { id: "PE-Year2", name: "Year 2", type: "year", children: [] },
        { id: "PE-Year3", name: "Year 3", type: "year", children: [] },
        { id: "PE-Year4", name: "Year 4", type: "year", children: [] },
        { id: "PE-Year5", name: "Year 5", type: "year", children: [] }
      ]
    },
    {
      id: "OB",
      name: "OB – Obstetrics & Gynecology",
      type: "program",
      children: []
    }
  ]);

  const handleSlotChange = (day, value) => {
    setWeeklySlots((prev) => ({
      ...prev,
      [day]: value,
    }));
  };

  const handleClear = (day) => {
    setWeeklySlots((prev) => ({
      ...prev,
      [day]: [],
    }));
  };

  const handleToggleExpand = (itemId) => {
    setExpandedItems(prev => ({
      ...prev,
      [itemId]: !prev[itemId]
    }));
  };

  const handleOpenOrderDialog = () => {
    setOrderDialogOpen(true);
  };

  const handleCloseOrderDialog = () => {
    setOrderDialogOpen(false);
  };

  const handleSaveOrder = () => {
    // Save the order logic here
    setOrderDialogOpen(false);
  };

  const handleResetOrder = () => {
    // Reset to original order logic here
  };

  const renderTreeItem = (item, level = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems[item.id];
    const paddingLeft = level * 20 + 16;

    return (
      <Box key={item.id}>
        <ListItem
          sx={{
            pl: `${paddingLeft}px`,
            py: 0.5,
            backgroundColor: item.type === 'course' && level === 3 ? '#f5f5f5' : 'transparent',
            '&:hover': {
              backgroundColor: '#f0f0f0'
            }
          }}
        >
          <DragIndicator sx={{ mr: 1, color: 'text.secondary', cursor: 'grab' }} />
          <ListItemText
            primary={item.name}
            sx={{
              '& .MuiListItemText-primary': {
                fontSize: '0.875rem',
                fontWeight: item.type === 'program' ? 600 : 400
              }
            }}
          />
          {hasChildren && (
            <IconButton
              size="small"
              onClick={() => handleToggleExpand(item.id)}
              sx={{ ml: 1 }}
            >
              {isExpanded ? <ExpandLess /> : <ExpandMore />}
            </IconButton>
          )}
        </ListItem>
        {hasChildren && (
          <Collapse in={isExpanded} timeout="auto" unmountOnExit>
            {item.children.map(child => renderTreeItem(child, level + 1))}
          </Collapse>
        )}
      </Box>
    );
  };

  const slotOptions = ["Slot 1", "Slot 2", "Slot 3"];

  const renderSlotValue = (selected) => {
    if (selected.length === 0) {
      return (
        <Typography variant="body2" color="text.secondary">
          - Select -
        </Typography>
      );
    }
    if (selected.length === slotOptions.length) {
      return <Typography variant="body2">All slots</Typography>;
    }
    if (selected.length > 2) {
      return (
        <Typography variant="body2">{selected.length} selected</Typography>
      );
    }
    return (
      <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.5 }}>
        {selected.map((value) => (
          <Chip key={value} label={value} size="small" />
        ))}
      </Box>
    );
  };

  const days = [
    "Sunday",
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
  ];

  return (
    <>
      <Card elevation={2}>
        <CardContent>
          <Box sx={{ display: "flex", gap: 4, alignItems: "flex-start" }}>
      {/* Left Column - Exam Slots for Each Day */}
      <Box sx={{ flex: 1, minWidth: 0 }}>
        <Typography variant="h6" component="h2" sx={{ fontWeight: 600, mb: 1 }}>
          Exam Slots for Each Day in a Week
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Assign slots for each day in a week
        </Typography>

        <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
          {days.map((day) => (
            <Box
              key={day}
              sx={{ display: "flex", alignItems: "center", gap: 2 }}
            >
              <Typography
                variant="body2"
                sx={{
                  minWidth: 80,
                  fontWeight: 500,
                  color: "text.primary",
                }}
              >
                {day}
              </Typography>
              <Typography variant="body2" sx={{ mx: 1 }}>
                :
              </Typography>
              <FormControl size="small" sx={{ minWidth: 200 }}>
                <Select
                  multiple
                  value={weeklySlots[day]}
                  onChange={(e) => handleSlotChange(day, e.target.value)}
                  input={<OutlinedInput />}
                  displayEmpty
                  sx={{ fontSize: "0.875rem" }}
                  renderValue={renderSlotValue}
                >
                  {slotOptions.map((option) => (
                    <MenuItem key={option} value={option}>
                      <Checkbox
                        checked={weeklySlots[day].indexOf(option) > -1}
                      />
                      <ListItemText primary={option} />
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              <Button
                size="small"
                color="primary"
                onClick={() => handleClear(day)}
                sx={{ textTransform: "none", fontSize: "0.875rem" }}
              >
                Clear
              </Button>
            </Box>
          ))}
        </Box>
      </Box>
      {/* Right Column */}
      <Box sx={{ flex: 1, minWidth: 0 }}>
        {/* Exam Gap Section */}
        <Box sx={{ mb: 4 }}>
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              mb: 1,
            }}
          >
            <Typography variant="h6" component="h2" sx={{ fontWeight: 600 }}>
              Exam Gap
            </Typography>
            <Link
              href="#"
              color="primary"
              sx={{ textDecoration: "none", fontSize: "0.875rem" }}
            >
              Global Exam Settings
            </Link>
          </Box>

          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Set how exam gap should be managed across courses
          </Typography>

          <Typography variant="body2" sx={{ mb: 2, fontWeight: 500 }}>
            Select the method for applying gaps between exams:
          </Typography>

          <RadioGroup
            value={gapMethod}
            onChange={(e) => setGapMethod(e.target.value)}
            sx={{ mb: 3 }}
          >
            <FormControlLabel
              value="common-all"
              control={<Radio size="small" />}
              label={
                <Typography variant="body2">
                  Common Gap Across All Courses
                </Typography>
              }
            />
            <FormControlLabel
              value="common-program"
              control={<Radio size="small" />}
              label={
                <Typography variant="body2">
                  Common Gap Per Program (for all its courses)
                </Typography>
              }
            />
          </RadioGroup>

          <Box sx={{ display: "flex", alignItems: "center", gap: 2, mb: 2 }}>
            <Typography variant="body2" sx={{ fontWeight: 500 }}>
              Set a common gap:
            </Typography>
            <TextField
              size="small"
              type="number"
              value={commonGap}
              onChange={(e) => setCommonGap(e.target.value)}
              sx={{ width: 80 }}
              slotProps={{
                htmlInput: { min: 0 },
              }}
            />
            <Typography variant="body2" color="text.secondary">
              In Days
            </Typography>
          </Box>

          <Alert severity="warning" icon={<Warning />} sx={{ mb: 3 }}>
            Exam extends to 10/11/2025 due to 2 day gap, exceeding the set date
            range. Recommended gap: 1 day
          </Alert>
        </Box>

        {/* Exam Order Sequence */}
        <Box>
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              mb: 1,
            }}
          >
            <Typography variant="h6" component="h2" sx={{ fontWeight: 600 }}>
              Exam Order Sequence
            </Typography>
            <Link
              href="#"
              color="primary"
              sx={{ textDecoration: "none", fontSize: "0.875rem" }}
            >
              Global Exam Settings
            </Link>
          </Box>

          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Set exam order should be managed across courses
          </Typography>

          <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                p: 2,
                backgroundColor: "#f8f9fa",
                borderRadius: 1,
              }}
            >
              <Box>
                <Typography variant="body2" sx={{ fontWeight: 500 }}>
                  PE – Pediatrics
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                60 courses
              </Typography>
            </Box>

            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                p: 2,
                backgroundColor: "#f8f9fa",
                borderRadius: 1,
              }}
            >
              <Box>
                <Typography variant="body2" sx={{ fontWeight: 500 }}>
                  OB – Obstetrics & Gynecology
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                60 courses
              </Typography>
            </Box>
          </Box>

          <Box sx={{ display: "flex", justifyContent: "center", mt: 3 }}>
            <Button
              color="primary"
              sx={{ textTransform: "none" }}
              onClick={handleOpenOrderDialog}
            >
              Change Order
            </Button>
          </Box>
          </Box>
        </Box>
          </Box>
        </CardContent>
      </Card>

      {/* Exam Order Dialog */}
    <Dialog
      open={orderDialogOpen}
      onClose={handleCloseOrderDialog}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          minHeight: '600px'
        }
      }}
    >
      <DialogTitle
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          borderBottom: '2px solid #1976d2',
          pb: 2,
          mb: 0
        }}
      >
        <Typography variant="h6" sx={{ fontWeight: 600 }}>
          Exam Order Sequence
        </Typography>
        <IconButton onClick={handleCloseOrderDialog} size="small">
          <Close />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ pt: 2 }}>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          To re-order, hover over the "⋮⋮" click and hold it, then drag to rearrange the items as per your preference.
        </Typography>

        <Box
          sx={{
            border: '1px solid #e0e0e0',
            borderRadius: 1,
            minHeight: '400px',
            backgroundColor: '#fafafa'
          }}
        >
          <List sx={{ py: 0 }}>
            {examOrder.map(item => renderTreeItem(item))}
          </List>
        </Box>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 3, gap: 2 }}>
        <Button
          variant="outlined"
          onClick={handleResetOrder}
          sx={{ textTransform: 'none' }}
        >
          Reset
        </Button>
        <Button
          variant="outlined"
          onClick={handleCloseOrderDialog}
          sx={{ textTransform: 'none' }}
        >
          Cancel
        </Button>
        <Button
          variant="contained"
          onClick={handleSaveOrder}
          sx={{ textTransform: 'none' }}
        >
          Save
        </Button>
      </DialogActions>
    </Dialog>
    </>
  );
};

export default ExamScheduleSettings;
