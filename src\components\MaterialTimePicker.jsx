import React, { useState } from 'react'
import {
  TextField,
  Popover,
  Box,
  Typography,
  IconButton,
  Paper,
  Button
} from '@mui/material'
import { AccessTime } from '@mui/icons-material'
import dayjs from 'dayjs'

const MaterialTimePicker = ({ value, onChange, size = 'small', sx = {} }) => {
  const [anchorEl, setAnchorEl] = useState(null)
  const [selectedHour, setSelectedHour] = useState(value ? value.hour() % 12 || 12 : 12)
  const [selectedMinute, setSelectedMinute] = useState(value ? value.minute() : 0)
  const [isAM, setIsAM] = useState(value ? value.hour() < 12 : true)
  const [isSelectingMinutes, setIsSelectingMinutes] = useState(false)

  const open = Boolean(anchorEl)

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget)
  }

  const handleClose = () => {
    setAnchorEl(null)
    setIsSelectingMinutes(false)
  }

  const handleTimeChange = () => {
    const hour24 = isAM ? (selectedHour === 12 ? 0 : selectedHour) : (selectedHour === 12 ? 12 : selectedHour + 12)
    const newTime = dayjs().hour(hour24).minute(selectedMinute)
    onChange(newTime)
    handleClose()
  }

  const formatTime = () => {
    if (!value) return ''
    return value.format('h:mm A')
  }

  const renderClock = () => {
    const numbers = isSelectingMinutes 
      ? Array.from({ length: 12 }, (_, i) => i * 5)
      : Array.from({ length: 12 }, (_, i) => i + 1)
    
    const selectedValue = isSelectingMinutes ? selectedMinute : selectedHour
    const radius = 100
    const centerX = 140
    const centerY = 140

    return (
      <Box sx={{ position: 'relative', width: 280, height: 280, p: 2 }}>
        {/* Clock Face */}
        <Box
          sx={{
            position: 'absolute',
            top: 20,
            left: 20,
            width: 240,
            height: 240,
            borderRadius: '50%',
            border: '1px solid',
            borderColor: 'divider',
            backgroundColor: 'background.paper'
          }}
        >
          {/* Clock Numbers */}
          {numbers.map((num, index) => {
            const angle = (index * 30 - 90) * (Math.PI / 180)
            const x = centerX + radius * Math.cos(angle) - 20
            const y = centerY + radius * Math.sin(angle) - 20
            const isSelected = isSelectingMinutes 
              ? num === selectedMinute 
              : num === selectedHour

            return (
              <Box
                key={num}
                onClick={() => {
                  if (isSelectingMinutes) {
                    setSelectedMinute(num)
                  } else {
                    setSelectedHour(num)
                    setIsSelectingMinutes(true)
                  }
                }}
                sx={{
                  position: 'absolute',
                  left: x,
                  top: y,
                  width: 40,
                  height: 40,
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  cursor: 'pointer',
                  backgroundColor: isSelected ? 'primary.main' : 'transparent',
                  color: isSelected ? 'primary.contrastText' : 'text.primary',
                  fontWeight: isSelected ? 600 : 400,
                  fontSize: '1rem',
                  '&:hover': {
                    backgroundColor: isSelected ? 'primary.main' : 'action.hover'
                  }
                }}
              >
                {isSelectingMinutes ? num.toString().padStart(2, '0') : num}
              </Box>
            )
          })}

          {/* Clock Hand */}
          {(() => {
            const currentValue = isSelectingMinutes ? selectedMinute / 5 : selectedHour - 1
            const angle = (currentValue * 30 - 90) * (Math.PI / 180)
            const handLength = isSelectingMinutes ? 80 : 60
            const endX = centerX + handLength * Math.cos(angle) - 20
            const endY = centerY + handLength * Math.sin(angle) - 20

            return (
              <>
                {/* Hand Line */}
                <Box
                  sx={{
                    position: 'absolute',
                    left: centerX - 20,
                    top: centerY - 20,
                    width: 2,
                    height: handLength,
                    backgroundColor: 'primary.main',
                    transformOrigin: '1px 0px',
                    transform: `rotate(${(currentValue * 30)}deg)`,
                    zIndex: 2
                  }}
                />
                {/* Center Dot */}
                <Box
                  sx={{
                    position: 'absolute',
                    left: centerX - 24,
                    top: centerY - 24,
                    width: 8,
                    height: 8,
                    borderRadius: '50%',
                    backgroundColor: 'primary.main',
                    zIndex: 3
                  }}
                />
                {/* Hand End Dot */}
                <Box
                  sx={{
                    position: 'absolute',
                    left: endX - 2,
                    top: endY - 2,
                    width: 16,
                    height: 16,
                    borderRadius: '50%',
                    backgroundColor: 'primary.main',
                    border: '2px solid white',
                    zIndex: 3
                  }}
                />
              </>
            )
          })()}
        </Box>

        {/* Time Display */}
        <Box sx={{ textAlign: 'center', mt: 1 }}>
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            {selectedHour.toString().padStart(2, '0')}:{selectedMinute.toString().padStart(2, '0')}
          </Typography>
          <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1, mt: 1 }}>
            <Button
              variant={isAM ? 'contained' : 'outlined'}
              size="small"
              onClick={() => setIsAM(true)}
              sx={{ minWidth: 40 }}
            >
              AM
            </Button>
            <Button
              variant={!isAM ? 'contained' : 'outlined'}
              size="small"
              onClick={() => setIsAM(false)}
              sx={{ minWidth: 40 }}
            >
              PM
            </Button>
          </Box>
        </Box>

        {/* Action Buttons */}
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1, mt: 2 }}>
          <Button onClick={handleClose} size="small">
            Cancel
          </Button>
          <Button onClick={handleTimeChange} variant="contained" size="small">
            OK
          </Button>
        </Box>
      </Box>
    )
  }

  return (
    <>
      <TextField
        size={size}
        variant="outlined"
        value={formatTime()}
        onClick={handleClick}
        InputProps={{
          readOnly: true,
          endAdornment: (
            <IconButton size="small" onClick={handleClick}>
              <AccessTime />
            </IconButton>
          )
        }}
        sx={{
          width: 140,
          cursor: 'pointer',
          '& .MuiInputBase-input': {
            cursor: 'pointer',
            fontSize: '0.875rem'
          },
          ...sx
        }}
      />
      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        PaperProps={{
          sx: {
            boxShadow: '0px 5px 5px -3px rgba(0,0,0,0.2), 0px 8px 10px 1px rgba(0,0,0,0.14), 0px 3px 14px 2px rgba(0,0,0,0.12)',
            borderRadius: '8px',
            overflow: 'visible'
          }
        }}
      >
        {renderClock()}
      </Popover>
    </>
  )
}

export default MaterialTimePicker
