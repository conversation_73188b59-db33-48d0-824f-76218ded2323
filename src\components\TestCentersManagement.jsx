import React, { useState } from 'react'
import {
  Box,
  Typography,
  TextField,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  InputAdornment,
  IconButton,
  Pagination,
  FormControl,
  Select,
  MenuItem,
  Checkbox,
  FormControlLabel,
  OutlinedInput,
  ListItemText,
  Chip,
  Card,
  CardContent
} from '@mui/material'
import { Search, FilterList, Warning } from '@mui/icons-material'

const TestCentersManagement = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [page, setPage] = useState(1)
  const [rowsPerPage, setRowsPerPage] = useState(10)
  const [selectedCenters, setSelectedCenters] = useState([
    '102', '103', '104', '105', '106', '107', '108', '109', '110'
  ])

  const testCenters = [
    {
      id: '101',
      name: 'Test center 01',
      capacity: 100,
      proctors: 2,
      programs: [],
      courses: []
    },
    {
      id: '102',
      name: 'Test center 02',
      capacity: 100,
      proctors: 2,
      programs: ['Program 1', 'Program 2', 'Program 3', 'Program 4', 'Program 5'],
      courses: Array.from({length: 250}, (_, i) => `Course ${i + 1}`)
    },
    {
      id: '103',
      name: 'Test center 03',
      capacity: 100,
      proctors: 2,
      programs: ['Program 1', 'Program 2', 'Program 3', 'Program 4', 'Program 5'],
      courses: Array.from({length: 250}, (_, i) => `Course ${i + 1}`)
    },
    {
      id: '104',
      name: 'Test center 04',
      capacity: 100,
      proctors: 2,
      programs: ['Program 1', 'Program 2', 'Program 3', 'Program 4', 'Program 5'],
      courses: Array.from({length: 250}, (_, i) => `Course ${i + 1}`)
    },
    {
      id: '105',
      name: 'Test center 05',
      capacity: 100,
      proctors: 2,
      programs: ['Program 1', 'Program 2', 'Program 3', 'Program 4', 'Program 5'],
      courses: Array.from({length: 250}, (_, i) => `Course ${i + 1}`)
    },
    {
      id: '106',
      name: 'Test center 06',
      capacity: 100,
      proctors: 2,
      programs: ['Program 1', 'Program 2', 'Program 3', 'Program 4', 'Program 5'],
      courses: Array.from({length: 250}, (_, i) => `Course ${i + 1}`)
    },
    {
      id: '107',
      name: 'Test center 07',
      capacity: 100,
      proctors: 2,
      programs: ['Program 1', 'Program 2', 'Program 3', 'Program 4', 'Program 5'],
      courses: Array.from({length: 250}, (_, i) => `Course ${i + 1}`)
    },
    {
      id: '108',
      name: 'Test center 08',
      capacity: 100,
      proctors: 2,
      programs: ['Program 1', 'Program 2', 'Program 3', 'Program 4', 'Program 5'],
      courses: Array.from({length: 250}, (_, i) => `Course ${i + 1}`)
    },
    {
      id: '109',
      name: 'Test center 09',
      capacity: 100,
      proctors: 2,
      programs: ['Program 1', 'Program 2', 'Program 3', 'Program 4', 'Program 5'],
      courses: Array.from({length: 250}, (_, i) => `Course ${i + 1}`)
    },
    {
      id: '110',
      name: 'Test center 10',
      capacity: 100,
      proctors: 2,
      programs: ['Program 1', 'Program 2', 'Program 3', 'Program 4', 'Program 5'],
      courses: Array.from({length: 250}, (_, i) => `Course ${i + 1}`)
    }
  ]

  const allPrograms = ['Program 1', 'Program 2', 'Program 3', 'Program 4', 'Program 5']
  const allCourses = Array.from({length: 250}, (_, i) => `Course ${i + 1}`)

  const handleCenterToggle = (centerId) => {
    setSelectedCenters(prev =>
      prev.includes(centerId)
        ? prev.filter(id => id !== centerId)
        : [...prev, centerId]
    )
  }

  const handleProgramChange = (centerId, programs) => {
    // Handle program selection change
    console.log(`Programs for ${centerId}:`, programs)
  }

  const handleCourseChange = (centerId, courses) => {
    // Handle course selection change
    console.log(`Courses for ${centerId}:`, courses)
  }

  const renderMultiSelectValue = (selected, total, type) => {
    if (selected.length === 0) {
      return (
        <Typography variant="body2" color="text.secondary">
          Tick the checkbox to enable
        </Typography>
      )
    }
    if (selected.length === total) {
      return (
        <Typography variant="body2">
          ({selected.length}) All {type} selected
        </Typography>
      )
    }
    return (
      <Typography variant="body2">
        ({selected.length}) {type} selected
      </Typography>
    )
  }

  const selectedCount = selectedCenters.length
  const totalCount = testCenters.length

  return (
    <Card elevation={2}>
      <CardContent>
        <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Box>
          <Typography variant="h6" component="h2" sx={{ fontWeight: 600, mb: 0.5 }}>
            Test Centers Involved
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Select the test centers participating in the examination
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            Selected: {selectedCount}/{totalCount}
          </Typography>
          <TextField
            size="small"
            placeholder="Search TC"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search fontSize="small" />
                </InputAdornment>
              ),
            }}
            sx={{ width: 200 }}
          />
          <IconButton>
            <FilterList />
          </IconButton>
        </Box>
      </Box>

      {/* Warning Alert */}
      <Alert severity="error" icon={<Warning />} sx={{ mb: 3 }}>
        Student count exceeds test center capacity by male: 50, female: 50. Increase 100 capacity otherwise these students will not be scheduled
      </Alert>

      {/* Table */}
      <TableContainer component={Paper} variant="outlined" sx={{ boxShadow: 'none', border: '1px solid', borderColor: 'divider' }}>
        <Table>
          <TableHead>
            <TableRow sx={{ backgroundColor: '#f8f9fa' }}>
              <TableCell sx={{ fontWeight: 600, color: 'text.secondary', fontSize: '0.875rem', width: 300 }}>
                Test Center Details
              </TableCell>
              <TableCell sx={{ fontWeight: 600, color: 'text.secondary', fontSize: '0.875rem' }}>
                Applicable Programs
              </TableCell>
              <TableCell sx={{ fontWeight: 600, color: 'text.secondary', fontSize: '0.875rem' }}>
                Applicable Courses
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {testCenters.slice((page - 1) * rowsPerPage, page * rowsPerPage).map((center) => (
              <TableRow key={center.id} hover>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Checkbox
                      checked={selectedCenters.includes(center.id)}
                      onChange={() => handleCenterToggle(center.id)}
                      color="primary"
                    />
                    <Box>
                      <Typography variant="body2" sx={{ fontWeight: 500, mb: 0.5 }}>
                        {center.id} - {center.name}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Male • {center.capacity} capacity • {center.proctors} proctors
                      </Typography>
                    </Box>
                  </Box>
                </TableCell>
                <TableCell>
                  <FormControl size="small" sx={{ minWidth: 200 }}>
                    <Select
                      multiple
                      value={center.programs}
                      onChange={(e) => handleProgramChange(center.id, e.target.value)}
                      input={<OutlinedInput />}
                      disabled={!selectedCenters.includes(center.id)}
                      displayEmpty
                      renderValue={(selected) => renderMultiSelectValue(selected, allPrograms.length, 'programs')}
                      sx={{ fontSize: '0.875rem' }}
                    >
                      {allPrograms.map((program) => (
                        <MenuItem key={program} value={program}>
                          <Checkbox checked={center.programs.indexOf(program) > -1} />
                          <ListItemText primary={program} />
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </TableCell>
                <TableCell>
                  <FormControl size="small" sx={{ minWidth: 200 }}>
                    <Select
                      multiple
                      value={center.courses}
                      onChange={(e) => handleCourseChange(center.id, e.target.value)}
                      input={<OutlinedInput />}
                      disabled={!selectedCenters.includes(center.id)}
                      displayEmpty
                      renderValue={(selected) => renderMultiSelectValue(selected, allCourses.length, 'courses')}
                      sx={{ fontSize: '0.875rem' }}
                    >
                      {allCourses.map((course) => (
                        <MenuItem key={course} value={course}>
                          <Checkbox checked={center.courses.indexOf(course) > -1} />
                          <ListItemText primary={course} />
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Pagination */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 2 }}>
        <Pagination
          count={Math.ceil(testCenters.length / rowsPerPage)}
          page={page}
          onChange={(e, newPage) => setPage(newPage)}
          color="primary"
        />
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Typography variant="body2">Lists per page:</Typography>
          <FormControl size="small">
            <Select
              value={rowsPerPage}
              onChange={(e) => setRowsPerPage(e.target.value)}
              sx={{ minWidth: 60 }}
            >
              <MenuItem value={10}>10</MenuItem>
              <MenuItem value={25}>25</MenuItem>
              <MenuItem value={50}>50</MenuItem>
            </Select>
          </FormControl>
        </Box>
      </Box>
        </Box>
      </CardContent>
    </Card>
  )
}

export default TestCentersManagement
