* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f8f9fa;
  color: #333;
  line-height: 1.5;
}

.app {
  min-height: 100vh;
  padding: 20px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.section {
  padding: 24px;
  border-bottom: 1px solid #e5e5e5;
}

.section:last-child {
  border-bottom: none;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.section-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.global-settings {
  color: #007bff;
  text-decoration: none;
  font-size: 14px;
}

.global-settings:hover {
  text-decoration: underline;
}

.section-subtitle {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #666;
}

.warning-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background-color: #fff5f5;
  border: 1px solid #fed7d7;
  border-radius: 6px;
  color: #c53030;
  font-size: 14px;
  margin-bottom: 16px;
}

.table-header {
  display: grid;
  grid-template-columns: 120px 100px 20px 100px 200px 200px 40px;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid #e5e5e5;
  font-weight: 600;
  font-size: 14px;
  color: #666;
  align-items: center;
}

.proctor-table-header {
  display: grid;
  grid-template-columns: 80px 100px 20px 100px 200px 40px;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid #e5e5e5;
  font-weight: 600;
  font-size: 14px;
  color: #666;
  align-items: center;
}

.table-row {
  display: grid;
  grid-template-columns: 120px 100px 20px 100px 200px 200px 40px;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  align-items: center;
}

.proctor-table-row {
  display: grid;
  grid-template-columns: 80px 100px 20px 100px 200px 40px;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  align-items: center;
}

.slot-label {
  display: flex;
  align-items: center;
  gap: 8px;
}

.slot-number {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  color: #666;
  text-align: center;
  min-width: 40px;
  line-height: 1.2;
}

.label-input {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  width: 100%;
  max-width: 80px;
}

.time-input input {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  width: 100%;
  text-align: center;
}

.time-separator {
  text-align: center;
  color: #666;
  font-size: 14px;
}

.dropdown-container select {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  width: 100%;
  background-color: white;
  cursor: pointer;
}

.delete-btn {
  background: none;
  border: none;
  color: #dc3545;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-btn:hover {
  background-color: #f8f9fa;
}

.add-slot-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  color: #007bff;
  cursor: pointer;
  padding: 12px 0;
  font-size: 14px;
  margin-top: 8px;
}

.add-slot-btn:hover {
  text-decoration: underline;
}

.checkbox-container {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e5e5;
}

.checkbox-container input[type="checkbox"] {
  margin: 0;
}

.checkbox-container label {
  font-size: 14px;
  color: #333;
  cursor: pointer;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 16px;
}

.summary-card {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
}

.summary-card h3 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #495057;
}

.summary-card p {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #6c757d;
}

.summary-card ul {
  margin: 0;
  padding-left: 16px;
  font-size: 14px;
  color: #495057;
}

.summary-card li {
  margin-bottom: 4px;
}

/* Responsive design */
@media (max-width: 768px) {
  .table-header,
  .table-row {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .proctor-table-header,
  .proctor-table-row {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .slot-label {
    flex-direction: column;
    align-items: flex-start;
  }

  .container {
    margin: 0;
    border-radius: 0;
  }

  .summary-grid {
    grid-template-columns: 1fr;
  }
}
