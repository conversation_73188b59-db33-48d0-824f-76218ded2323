import React from 'react'
import {
  Card,
  CardContent,
  Typography,
  Button,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  IconButton,
  Alert,
  Box,
  Grid,
  Chip,
  Link,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  OutlinedInput,
  ListItemText,
  Checkbox
} from '@mui/material'
import { Add, Delete, Warning } from '@mui/icons-material'
import { TimePicker } from '@mui/x-date-pickers/TimePicker'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import dayjs from 'dayjs'

const ExamTimingsSection = ({
  examTimings,
  setExamTimings,
  programOptions,
  courseOptions
}) => {
  // Add new exam timing
  const addExamTiming = () => {
    const newId = examTimings.length > 0 ? Math.max(...examTimings.map(t => t.id)) + 1 : 1
    const newTiming = {
      id: newId,
      label: `Slot ${String(newId).padStart(2, '0')}`,
      startTime: dayjs().hour(9).minute(0),
      endTime: dayjs().hour(11).minute(0),
      programs: [],
      courses: []
    }
    setExamTimings([...examTimings, newTiming])
  }

  // Delete exam timing
  const deleteExamTiming = (id) => {
    if (examTimings.length > 1) {
      setExamTimings(examTimings.filter(t => t.id !== id))
    } else {
      alert('Cannot delete the last exam timing slot')
    }
  }

  // Update exam timing
  const updateExamTiming = (id, field, value) => {
    setExamTimings(examTimings.map(t =>
      t.id === id ? { ...t, [field]: value } : t
    ))
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Card elevation={2}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
            <Typography variant="h5" component="h2" sx={{ fontWeight: 600 }}>
              Exam Timings Per Day
            </Typography>
            <Link href="#" color="primary" sx={{ textDecoration: 'none' }}>
              Global Exam Settings
            </Link>
          </Box>

          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Set your institution's daily examination timings here
          </Typography>

          {/* Warning Message */}
          <Alert severity="warning" icon={<Warning />} sx={{ mb: 3 }}>
            Exam duration exceeds exam timings. Recommended timing: ≤ 3 hrs
          </Alert>

          {/* Table */}
          <TableContainer component={Paper} variant="outlined">
            <Table>
              <TableHead>
                <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
                  <TableCell sx={{ fontWeight: 600 }}>Label</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Start Time</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>End Time</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Applicable Programs</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Applicable Courses</TableCell>
                  <TableCell sx={{ fontWeight: 600, width: 60 }}>Actions</TableCell>
                </TableRow>
              </TableHead>
            <TableBody>
              {/* Exam Timing Rows */}
              {examTimings.map((timing) => (
                <TableRow key={timing.id} hover>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Chip
                        label={`Slot ${String(timing.id).padStart(2, '0')}`}
                        size="small"
                        variant="outlined"
                      />
                      <TextField
                        size="small"
                        value={timing.label}
                        onChange={(e) => updateExamTiming(timing.id, 'label', e.target.value)}
                        sx={{ minWidth: 100 }}
                      />
                    </Box>
                  </TableCell>
                  <TableCell>
                    <TimePicker
                      value={timing.startTime}
                      onChange={(newValue) => updateExamTiming(timing.id, 'startTime', newValue)}
                      slotProps={{
                        textField: {
                          size: 'small',
                          sx: { width: 140 }
                        }
                      }}
                    />
                  </TableCell>
                  <TableCell>
                    <TimePicker
                      value={timing.endTime}
                      onChange={(newValue) => updateExamTiming(timing.id, 'endTime', newValue)}
                      slotProps={{
                        textField: {
                          size: 'small',
                          sx: { width: 140 }
                        }
                      }}
                    />
                  </TableCell>
                  <TableCell>
                    <FormControl size="small" sx={{ minWidth: 180 }}>
                      <InputLabel>Programs</InputLabel>
                      <Select
                        multiple
                        value={timing.programs}
                        onChange={(e) => updateExamTiming(timing.id, 'programs', e.target.value)}
                        input={<OutlinedInput label="Programs" />}
                        renderValue={(selected) => (
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                            {selected.length === 0 ? (
                              <Typography variant="body2" color="text.secondary">
                                None selected
                              </Typography>
                            ) : selected.length === programOptions.length ? (
                              <Chip size="small" label={`All (${selected.length})`} />
                            ) : selected.length > 4 ? (
                              <Chip size="small" label={`${selected.length} programs selected`} />
                            ) : (
                              selected.map((value) => (
                                <Chip key={value} label={value} size="small" />
                              ))
                            )}
                          </Box>
                        )}
                      >
                        {programOptions.map((program) => (
                          <MenuItem key={program} value={program}>
                            <Checkbox checked={timing.programs.indexOf(program) > -1} />
                            <ListItemText primary={program} />
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </TableCell>
                  <TableCell>
                    <FormControl size="small" sx={{ minWidth: 180 }}>
                      <InputLabel>Courses</InputLabel>
                      <Select
                        multiple
                        value={timing.courses}
                        onChange={(e) => updateExamTiming(timing.id, 'courses', e.target.value)}
                        input={<OutlinedInput label="Courses" />}
                        renderValue={(selected) => (
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                            {selected.length === 0 ? (
                              <Typography variant="body2" color="text.secondary">
                                None selected
                              </Typography>
                            ) : selected.length === courseOptions.length ? (
                              <Chip size="small" label={`All (${selected.length})`} />
                            ) : selected.length > 4 ? (
                              <Chip size="small" label={`${selected.length} courses selected`} />
                            ) : (
                              selected.map((value) => (
                                <Chip key={value} label={value} size="small" />
                              ))
                            )}
                          </Box>
                        )}
                      >
                        {courseOptions.map((course) => (
                          <MenuItem key={course} value={course}>
                            <Checkbox checked={timing.courses.indexOf(course) > -1} />
                            <ListItemText primary={course} />
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </TableCell>
                  <TableCell>
                    <IconButton
                      color="error"
                      onClick={() => deleteExamTiming(timing.id)}
                      size="small"
                    >
                      <Delete />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Add Slot Button */}
        <Box sx={{ mt: 2 }}>
          <Button
            variant="outlined"
            startIcon={<Add />}
            onClick={addExamTiming}
            color="primary"
          >
            Slot {String(examTimings.length + 1).padStart(2, '0')}
          </Button>
        </Box>
      </CardContent>
    </Card>
    </LocalizationProvider>
  )
}

export default ExamTimingsSection
