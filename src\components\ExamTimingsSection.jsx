import React from 'react'
import { Plus, Trash2, AlertCircle } from 'lucide-react'

const ExamTimingsSection = ({
  examTimings,
  setExamTimings,
  programOptions,
  courseOptions
}) => {
  // Add new exam timing
  const addExamTiming = () => {
    const newId = examTimings.length > 0 ? Math.max(...examTimings.map(t => t.id)) + 1 : 1
    const newTiming = {
      id: newId,
      label: `Slot ${String(newId).padStart(2, '0')}`,
      startTime: '9:00 AM',
      endTime: '11:00 AM',
      programs: 'All selected',
      courses: 'All selected'
    }
    setExamTimings([...examTimings, newTiming])
  }

  // Delete exam timing
  const deleteExamTiming = (id) => {
    if (examTimings.length > 1) {
      setExamTimings(examTimings.filter(t => t.id !== id))
    } else {
      alert('Cannot delete the last exam timing slot')
    }
  }

  // Update exam timing
  const updateExamTiming = (id, field, value) => {
    setExamTimings(examTimings.map(t =>
      t.id === id ? { ...t, [field]: value } : t
    ))
  }

  return (
    <div className="section">
      <div className="section-header">
        <h2>Exam Timings Per Day</h2>
        <a href="#" className="global-settings">Global Exam Settings</a>
      </div>
      <p className="section-subtitle">Set your institution's daily examination timings here</p>

      {/* Warning Message */}
      <div className="warning-message">
        <AlertCircle size={16} />
        <span>Exam duration exceeds exam timings. Recommended timing: ≤ 3 hrs</span>
      </div>

      {/* Table Header */}
      <div className="table-header">
        <div className="col-label">Label</div>
        <div className="col-time">Start time</div>
        <div className="col-time">End time</div>
        <div className="col-dropdown">Applicable programs</div>
        <div className="col-dropdown">Applicable courses</div>
        <div className="col-action"></div>
      </div>

      {/* Exam Timing Rows */}
      {examTimings.map((timing) => (
        <div key={timing.id} className="table-row">
          <div className="slot-label">
            <span className="slot-number">Slot<br/>{String(timing.id).padStart(2, '0')}</span>
            <input
              type="text"
              value={timing.label}
              onChange={(e) => updateExamTiming(timing.id, 'label', e.target.value)}
              className="label-input"
            />
          </div>
          <div className="time-input">
            <input
              type="text"
              value={timing.startTime}
              onChange={(e) => updateExamTiming(timing.id, 'startTime', e.target.value)}
            />
          </div>
          <div className="time-separator">to</div>
          <div className="time-input">
            <input
              type="text"
              value={timing.endTime}
              onChange={(e) => updateExamTiming(timing.id, 'endTime', e.target.value)}
            />
          </div>
          <div className="dropdown-container">
            <select
              value={timing.programs}
              onChange={(e) => updateExamTiming(timing.id, 'programs', e.target.value)}
            >
              <option value="All selected">(2) All selected</option>
              {programOptions.map(program => (
                <option key={program} value={program}>{program}</option>
              ))}
            </select>
          </div>
          <div className="dropdown-container">
            <select
              value={timing.courses}
              onChange={(e) => updateExamTiming(timing.id, 'courses', e.target.value)}
            >
              <option value="All selected">(120) All selected</option>
              {courseOptions.map(course => (
                <option key={course} value={course}>{course}</option>
              ))}
            </select>
          </div>
          <button
            className="delete-btn"
            onClick={() => deleteExamTiming(timing.id)}
          >
            <Trash2 size={16} />
          </button>
        </div>
      ))}

      {/* Add Slot Button */}
      <button className="add-slot-btn" onClick={addExamTiming}>
        <Plus size={16} />
        Slot {String(examTimings.length + 1).padStart(2, '0')}
      </button>
    </div>
  )
}

export default ExamTimingsSection
