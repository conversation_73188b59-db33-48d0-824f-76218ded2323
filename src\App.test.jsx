import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect } from 'vitest'
import App from './App'

describe('Exam Scheduling App', () => {
  it('renders exam timings section', () => {
    render(<App />)
    expect(screen.getByText('Exam Timings Per Day')).toBeInTheDocument()
    expect(screen.getByText('Set your institution\'s daily examination timings here')).toBeInTheDocument()
  })

  it('renders proctor duty timings section', () => {
    render(<App />)
    expect(screen.getByText('Proctor Duty Timings')).toBeInTheDocument()
    expect(screen.getByText('Set your institution\'s daily proctor duty timings here')).toBeInTheDocument()
  })

  it('can add new exam timing slot', () => {
    render(<App />)
    const addButton = screen.getByText(/Slot 04/)
    fireEvent.click(addButton)
    expect(screen.getByText(/Slot 05/)).toBeInTheDocument()
  })

  it('can delete exam timing slot', () => {
    render(<App />)
    const deleteButtons = screen.getAllByRole('button')
    const examDeleteButton = deleteButtons.find(btn => 
      btn.querySelector('svg') && btn.closest('.table-row')
    )
    if (examDeleteButton) {
      fireEvent.click(examDeleteButton)
    }
  })

  it('can update exam timing fields', () => {
    render(<App />)
    const labelInput = screen.getAllByDisplayValue('Morning')[0]
    fireEvent.change(labelInput, { target: { value: 'Updated Morning' } })
    expect(labelInput.value).toBe('Updated Morning')
  })

  it('shows warning message', () => {
    render(<App />)
    expect(screen.getByText(/Exam duration exceeds exam timings/)).toBeInTheDocument()
  })

  it('has exclude proctor authors checkbox', () => {
    render(<App />)
    const checkbox = screen.getByLabelText(/Exclude proctors who are authors/)
    expect(checkbox).toBeChecked()
    fireEvent.click(checkbox)
    expect(checkbox).not.toBeChecked()
  })
})
