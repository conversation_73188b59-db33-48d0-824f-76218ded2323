import React from 'react'
import {
  <PERSON>,
  CardContent,
  Typography,
  Box,
  Grid,
  List,
  ListItem,
  ListItemText,
  Chip,
  Divider
} from '@mui/material'
import { Schedule, Person, Settings } from '@mui/icons-material'

const DataSummary = ({ examTimings, proctorTimings, excludeProctorAuthors }) => {
  return (
    <Card elevation={2}>
      <CardContent>
        <Typography variant="h5" component="h2" sx={{ fontWeight: 600, mb: 1 }}>
          Data Summary
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Current configuration summary
        </Typography>

        <Grid container spacing={3}>
          <Grid item xs={12} md={4}>
            <Card variant="outlined" sx={{ height: '100%' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Schedule color="primary" sx={{ mr: 1 }} />
                  <Typography variant="h6" component="h3">
                    Exam <PERSON>
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  <strong>Total Slots:</strong> {examTimings.length}
                </Typography>
                <List dense>
                  {examTimings.map(timing => (
                    <ListItem key={timing.id} sx={{ px: 0 }}>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Chip
                              label={timing.label}
                              size="small"
                              color="primary"
                              variant="outlined"
                            />
                            <Typography variant="body2">
                              {timing.startTime?.format('h:mm A')} - {timing.endTime?.format('h:mm A')}
                            </Typography>
                          </Box>
                        }
                        secondary={
                          <Box sx={{ mt: 0.5 }}>
                            <Typography variant="caption" color="text.secondary">
                              Programs: {timing.programs?.length > 4 ? `${timing.programs.length} selected` : timing.programs?.join(', ')}
                            </Typography>
                            <br />
                            <Typography variant="caption" color="text.secondary">
                              Courses: {timing.courses?.length > 4 ? `${timing.courses.length} selected` : timing.courses?.join(', ')}
                            </Typography>
                          </Box>
                        }
                      />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card variant="outlined" sx={{ height: '100%' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Person color="primary" sx={{ mr: 1 }} />
                  <Typography variant="h6" component="h3">
                    Proctor Duty Timings
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  <strong>Total Slots:</strong> {proctorTimings.length}
                </Typography>
                <List dense>
                  {proctorTimings.map(timing => (
                    <ListItem key={timing.id} sx={{ px: 0 }}>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Chip
                              label={`Slot ${String(timing.id).padStart(2, '0')}`}
                              size="small"
                              color="secondary"
                              variant="outlined"
                            />
                            <Typography variant="body2">
                              {timing.startTime?.format('h:mm A')} - {timing.endTime?.format('h:mm A')}
                            </Typography>
                          </Box>
                        }
                        secondary={
                          <Box sx={{ mt: 0.5 }}>
                            <Typography variant="caption" color="text.secondary">
                              Proctors: {timing.proctors?.length > 4 ? `${timing.proctors.length} selected` : timing.proctors?.join(', ')}
                            </Typography>
                          </Box>
                        }
                      />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card variant="outlined" sx={{ height: '100%' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Settings color="primary" sx={{ mr: 1 }} />
                  <Typography variant="h6" component="h3">
                    Settings
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Typography variant="body2" color="text.secondary">
                    Exclude Proctor Authors: <AUTHORS>
                  <Chip
                    label={excludeProctorAuthors ? 'Yes' : 'No'}
                    size="small"
                    color={excludeProctorAuthors ? 'success' : 'default'}
                    variant="filled"
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default DataSummary
